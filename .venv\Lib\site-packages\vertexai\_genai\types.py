# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import datetime
import importlib
import json
import logging
import re
import typing
from typing import Any, Callable, ClassVar, Literal, Optional, Tuple, TypeVar, Union
from google.genai import _common
from google.genai import types as genai_types
from pydantic import (
    ConfigDict,
    Field,
    PrivateAttr,
    computed_field,
    field_validator,
    model_validator,
)
from typing_extensions import TypedDict

logger = logging.getLogger("vertexai_genai.types")

__all__ = ["PrebuiltMetric"]  # noqa: F822


def __getattr__(name: str) -> typing.Any:
    if name == "PrebuiltMetric":
        module = importlib.import_module("._evals_utils", __package__)
        prebuilt_metric_obj = getattr(module, name)
        globals()[name] = prebuilt_metric_obj
        return prebuilt_metric_obj
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


if typing.TYPE_CHECKING:
    import pandas as pd
else:
    pd: typing.Type = Any
    try:
        import pandas as pd
    except ImportError:
        pd = None
if typing.TYPE_CHECKING:
    import yaml
else:
    try:
        import yaml
    except ImportError:
        yaml = None

logger = logging.getLogger("vertexai_genai.types")

MetricSubclass = TypeVar("MetricSubclass", bound="Metric")


class PairwiseChoice(_common.CaseInSensitiveEnum):
    """Output only. Pairwise metric choice."""

    PAIRWISE_CHOICE_UNSPECIFIED = "PAIRWISE_CHOICE_UNSPECIFIED"
    """Unspecified prediction choice."""
    BASELINE = "BASELINE"
    """Baseline prediction wins"""
    CANDIDATE = "CANDIDATE"
    """Candidate prediction wins"""
    TIE = "TIE"
    """Winner cannot be determined"""


class BleuInstance(_common.BaseModel):
    """Bleu instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class BleuInstanceDict(TypedDict, total=False):
    """Bleu instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


BleuInstanceOrDict = Union[BleuInstance, BleuInstanceDict]


class BleuSpec(_common.BaseModel):
    """Spec for bleu metric."""

    use_effective_order: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use_effective_order to compute bleu score.""",
    )


class BleuSpecDict(TypedDict, total=False):
    """Spec for bleu metric."""

    use_effective_order: Optional[bool]
    """Optional. Whether to use_effective_order to compute bleu score."""


BleuSpecOrDict = Union[BleuSpec, BleuSpecDict]


class BleuInput(_common.BaseModel):

    instances: Optional[list[BleuInstance]] = Field(
        default=None, description="""Required. Repeated bleu instances."""
    )
    metric_spec: Optional[BleuSpec] = Field(
        default=None, description="""Required. Spec for bleu score metric."""
    )


class BleuInputDict(TypedDict, total=False):

    instances: Optional[list[BleuInstanceDict]]
    """Required. Repeated bleu instances."""

    metric_spec: Optional[BleuSpecDict]
    """Required. Spec for bleu score metric."""


BleuInputOrDict = Union[BleuInput, BleuInputDict]


class ExactMatchInstance(_common.BaseModel):
    """Exact match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ExactMatchInstanceDict(TypedDict, total=False):
    """Exact match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ExactMatchInstanceOrDict = Union[ExactMatchInstance, ExactMatchInstanceDict]


class ExactMatchSpec(_common.BaseModel):
    """Spec for exact match metric."""

    pass


class ExactMatchSpecDict(TypedDict, total=False):
    """Spec for exact match metric."""

    pass


ExactMatchSpecOrDict = Union[ExactMatchSpec, ExactMatchSpecDict]


class ExactMatchInput(_common.BaseModel):

    instances: Optional[list[ExactMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated exact match instances.""",
    )
    metric_spec: Optional[ExactMatchSpec] = Field(
        default=None, description="""Required. Spec for exact match metric."""
    )


class ExactMatchInputDict(TypedDict, total=False):

    instances: Optional[list[ExactMatchInstanceDict]]
    """Required. Repeated exact match instances."""

    metric_spec: Optional[ExactMatchSpecDict]
    """Required. Spec for exact match metric."""


ExactMatchInputOrDict = Union[ExactMatchInput, ExactMatchInputDict]


class RougeInstance(_common.BaseModel):
    """Rouge instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class RougeInstanceDict(TypedDict, total=False):
    """Rouge instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


RougeInstanceOrDict = Union[RougeInstance, RougeInstanceDict]


class RougeSpec(_common.BaseModel):
    """Spec for rouge metric."""

    rouge_type: Optional[str] = Field(
        default=None,
        description="""Optional. Supported rouge types are rougen[1-9], rougeL, and rougeLsum.""",
    )
    split_summaries: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to split summaries while using rougeLsum.""",
    )
    use_stemmer: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use stemmer to compute rouge score.""",
    )


class RougeSpecDict(TypedDict, total=False):
    """Spec for rouge metric."""

    rouge_type: Optional[str]
    """Optional. Supported rouge types are rougen[1-9], rougeL, and rougeLsum."""

    split_summaries: Optional[bool]
    """Optional. Whether to split summaries while using rougeLsum."""

    use_stemmer: Optional[bool]
    """Optional. Whether to use stemmer to compute rouge score."""


RougeSpecOrDict = Union[RougeSpec, RougeSpecDict]


class RougeInput(_common.BaseModel):
    """Rouge input."""

    instances: Optional[list[RougeInstance]] = Field(
        default=None, description="""Required. Repeated rouge instances."""
    )
    metric_spec: Optional[RougeSpec] = Field(
        default=None, description="""Required. Spec for rouge score metric."""
    )


class RougeInputDict(TypedDict, total=False):
    """Rouge input."""

    instances: Optional[list[RougeInstanceDict]]
    """Required. Repeated rouge instances."""

    metric_spec: Optional[RougeSpecDict]
    """Required. Spec for rouge score metric."""


RougeInputOrDict = Union[RougeInput, RougeInputDict]


class PointwiseMetricInstance(_common.BaseModel):
    """Pointwise metric instance."""

    json_instance: Optional[str] = Field(
        default=None,
        description="""Instance specified as a json string. String key-value pairs are expected in the json_instance to render PointwiseMetricSpec.instance_prompt_template.""",
    )


class PointwiseMetricInstanceDict(TypedDict, total=False):
    """Pointwise metric instance."""

    json_instance: Optional[str]
    """Instance specified as a json string. String key-value pairs are expected in the json_instance to render PointwiseMetricSpec.instance_prompt_template."""


PointwiseMetricInstanceOrDict = Union[
    PointwiseMetricInstance, PointwiseMetricInstanceDict
]


class CustomOutputFormatConfig(_common.BaseModel):
    """Spec for custom output format configuration."""

    return_raw_output: Optional[bool] = Field(
        default=None, description="""Optional. Whether to return raw output."""
    )


class CustomOutputFormatConfigDict(TypedDict, total=False):
    """Spec for custom output format configuration."""

    return_raw_output: Optional[bool]
    """Optional. Whether to return raw output."""


CustomOutputFormatConfigOrDict = Union[
    CustomOutputFormatConfig, CustomOutputFormatConfigDict
]


class PointwiseMetricSpec(_common.BaseModel):
    """Spec for pointwise metric."""

    metric_prompt_template: Optional[str] = Field(
        default=None,
        description="""Required. Metric prompt template for pointwise metric.""",
    )
    custom_output_format_config: Optional[CustomOutputFormatConfig] = Field(
        default=None,
        description="""Optional. CustomOutputFormatConfig allows customization of metric output. By default, metrics return a score and explanation. When this config is set, the default output is replaced with either: - The raw output string. - A parsed output based on a user-defined schema. If a custom format is chosen, the `score` and `explanation` fields in the corresponding metric result will be empty.""",
    )
    system_instruction: Optional[str] = Field(
        default=None,
        description="""Optional. System instructions for pointwise metric.""",
    )


class PointwiseMetricSpecDict(TypedDict, total=False):
    """Spec for pointwise metric."""

    metric_prompt_template: Optional[str]
    """Required. Metric prompt template for pointwise metric."""

    custom_output_format_config: Optional[CustomOutputFormatConfigDict]
    """Optional. CustomOutputFormatConfig allows customization of metric output. By default, metrics return a score and explanation. When this config is set, the default output is replaced with either: - The raw output string. - A parsed output based on a user-defined schema. If a custom format is chosen, the `score` and `explanation` fields in the corresponding metric result will be empty."""

    system_instruction: Optional[str]
    """Optional. System instructions for pointwise metric."""


PointwiseMetricSpecOrDict = Union[PointwiseMetricSpec, PointwiseMetricSpecDict]


class PointwiseMetricInput(_common.BaseModel):
    """Pointwise metric input."""

    instance: Optional[PointwiseMetricInstance] = Field(
        default=None, description="""Required. Pointwise metric instance."""
    )
    metric_spec: Optional[PointwiseMetricSpec] = Field(
        default=None, description="""Required. Spec for pointwise metric."""
    )


class PointwiseMetricInputDict(TypedDict, total=False):
    """Pointwise metric input."""

    instance: Optional[PointwiseMetricInstanceDict]
    """Required. Pointwise metric instance."""

    metric_spec: Optional[PointwiseMetricSpecDict]
    """Required. Spec for pointwise metric."""


PointwiseMetricInputOrDict = Union[PointwiseMetricInput, PointwiseMetricInputDict]


class PairwiseMetricInstance(_common.BaseModel):
    """Pairwise metric instance."""

    json_instance: Optional[str] = Field(
        default=None,
        description="""Instance specified as a json string. String key-value pairs are expected in the json_instance to render PairwiseMetricSpec.instance_prompt_template.""",
    )


class PairwiseMetricInstanceDict(TypedDict, total=False):
    """Pairwise metric instance."""

    json_instance: Optional[str]
    """Instance specified as a json string. String key-value pairs are expected in the json_instance to render PairwiseMetricSpec.instance_prompt_template."""


PairwiseMetricInstanceOrDict = Union[PairwiseMetricInstance, PairwiseMetricInstanceDict]


class PairwiseMetricSpec(_common.BaseModel):
    """Spec for pairwise metric."""

    metric_prompt_template: Optional[str] = Field(
        default=None,
        description="""Required. Metric prompt template for pairwise metric.""",
    )
    baseline_response_field_name: Optional[str] = Field(
        default=None,
        description="""Optional. The field name of the baseline response.""",
    )
    candidate_response_field_name: Optional[str] = Field(
        default=None,
        description="""Optional. The field name of the candidate response.""",
    )
    custom_output_format_config: Optional[CustomOutputFormatConfig] = Field(
        default=None,
        description="""Optional. CustomOutputFormatConfig allows customization of metric output. When this config is set, the default output is replaced with the raw output string. If a custom format is chosen, the `pairwise_choice` and `explanation` fields in the corresponding metric result will be empty.""",
    )
    system_instruction: Optional[str] = Field(
        default=None,
        description="""Optional. System instructions for pairwise metric.""",
    )


class PairwiseMetricSpecDict(TypedDict, total=False):
    """Spec for pairwise metric."""

    metric_prompt_template: Optional[str]
    """Required. Metric prompt template for pairwise metric."""

    baseline_response_field_name: Optional[str]
    """Optional. The field name of the baseline response."""

    candidate_response_field_name: Optional[str]
    """Optional. The field name of the candidate response."""

    custom_output_format_config: Optional[CustomOutputFormatConfigDict]
    """Optional. CustomOutputFormatConfig allows customization of metric output. When this config is set, the default output is replaced with the raw output string. If a custom format is chosen, the `pairwise_choice` and `explanation` fields in the corresponding metric result will be empty."""

    system_instruction: Optional[str]
    """Optional. System instructions for pairwise metric."""


PairwiseMetricSpecOrDict = Union[PairwiseMetricSpec, PairwiseMetricSpecDict]


class PairwiseMetricInput(_common.BaseModel):
    """Pairwise metric instance."""

    instance: Optional[PairwiseMetricInstance] = Field(
        default=None, description="""Required. Pairwise metric instance."""
    )
    metric_spec: Optional[PairwiseMetricSpec] = Field(
        default=None, description="""Required. Spec for pairwise metric."""
    )


class PairwiseMetricInputDict(TypedDict, total=False):
    """Pairwise metric instance."""

    instance: Optional[PairwiseMetricInstanceDict]
    """Required. Pairwise metric instance."""

    metric_spec: Optional[PairwiseMetricSpecDict]
    """Required. Spec for pairwise metric."""


PairwiseMetricInputOrDict = Union[PairwiseMetricInput, PairwiseMetricInputDict]


class ToolCallValidInstance(_common.BaseModel):
    """Tool call valid instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolCallValidInstanceDict(TypedDict, total=False):
    """Tool call valid instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolCallValidInstanceOrDict = Union[ToolCallValidInstance, ToolCallValidInstanceDict]


class ToolCallValidSpec(_common.BaseModel):
    """Spec for tool call valid metric."""

    pass


class ToolCallValidSpecDict(TypedDict, total=False):
    """Spec for tool call valid metric."""

    pass


ToolCallValidSpecOrDict = Union[ToolCallValidSpec, ToolCallValidSpecDict]


class ToolCallValidInput(_common.BaseModel):
    """Tool call valid input."""

    instances: Optional[list[ToolCallValidInstance]] = Field(
        default=None,
        description="""Required. Repeated tool call valid instances.""",
    )
    metric_spec: Optional[ToolCallValidSpec] = Field(
        default=None,
        description="""Required. Spec for tool call valid metric.""",
    )


class ToolCallValidInputDict(TypedDict, total=False):
    """Tool call valid input."""

    instances: Optional[list[ToolCallValidInstanceDict]]
    """Required. Repeated tool call valid instances."""

    metric_spec: Optional[ToolCallValidSpecDict]
    """Required. Spec for tool call valid metric."""


ToolCallValidInputOrDict = Union[ToolCallValidInput, ToolCallValidInputDict]


class ToolNameMatchInstance(_common.BaseModel):
    """Tool name match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolNameMatchInstanceDict(TypedDict, total=False):
    """Tool name match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolNameMatchInstanceOrDict = Union[ToolNameMatchInstance, ToolNameMatchInstanceDict]


class ToolNameMatchSpec(_common.BaseModel):
    """Spec for tool name match metric."""

    pass


class ToolNameMatchSpecDict(TypedDict, total=False):
    """Spec for tool name match metric."""

    pass


ToolNameMatchSpecOrDict = Union[ToolNameMatchSpec, ToolNameMatchSpecDict]


class ToolNameMatchInput(_common.BaseModel):
    """Tool name match input."""

    instances: Optional[list[ToolNameMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool name match instances.""",
    )
    metric_spec: Optional[ToolNameMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool name match metric.""",
    )


class ToolNameMatchInputDict(TypedDict, total=False):
    """Tool name match input."""

    instances: Optional[list[ToolNameMatchInstanceDict]]
    """Required. Repeated tool name match instances."""

    metric_spec: Optional[ToolNameMatchSpecDict]
    """Required. Spec for tool name match metric."""


ToolNameMatchInputOrDict = Union[ToolNameMatchInput, ToolNameMatchInputDict]


class ToolParameterKeyMatchInstance(_common.BaseModel):
    """Tool parameter key match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolParameterKeyMatchInstanceDict(TypedDict, total=False):
    """Tool parameter key match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolParameterKeyMatchInstanceOrDict = Union[
    ToolParameterKeyMatchInstance, ToolParameterKeyMatchInstanceDict
]


class ToolParameterKeyMatchSpec(_common.BaseModel):
    """Spec for tool parameter key match metric."""

    pass


class ToolParameterKeyMatchSpecDict(TypedDict, total=False):
    """Spec for tool parameter key match metric."""

    pass


ToolParameterKeyMatchSpecOrDict = Union[
    ToolParameterKeyMatchSpec, ToolParameterKeyMatchSpecDict
]


class ToolParameterKeyMatchInput(_common.BaseModel):
    """Tool parameter key match input."""

    instances: Optional[list[ToolParameterKeyMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool parameter key match instances.""",
    )
    metric_spec: Optional[ToolParameterKeyMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool parameter key match metric.""",
    )


class ToolParameterKeyMatchInputDict(TypedDict, total=False):
    """Tool parameter key match input."""

    instances: Optional[list[ToolParameterKeyMatchInstanceDict]]
    """Required. Repeated tool parameter key match instances."""

    metric_spec: Optional[ToolParameterKeyMatchSpecDict]
    """Required. Spec for tool parameter key match metric."""


ToolParameterKeyMatchInputOrDict = Union[
    ToolParameterKeyMatchInput, ToolParameterKeyMatchInputDict
]


class ToolParameterKVMatchInstance(_common.BaseModel):
    """Tool parameter kv match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolParameterKVMatchInstanceDict(TypedDict, total=False):
    """Tool parameter kv match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolParameterKVMatchInstanceOrDict = Union[
    ToolParameterKVMatchInstance, ToolParameterKVMatchInstanceDict
]


class ToolParameterKVMatchSpec(_common.BaseModel):
    """Spec for tool parameter kv match metric."""

    use_strict_string_match: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use STRICT string match on parameter values.""",
    )


class ToolParameterKVMatchSpecDict(TypedDict, total=False):
    """Spec for tool parameter kv match metric."""

    use_strict_string_match: Optional[bool]
    """Optional. Whether to use STRICT string match on parameter values."""


ToolParameterKVMatchSpecOrDict = Union[
    ToolParameterKVMatchSpec, ToolParameterKVMatchSpecDict
]


class ToolParameterKVMatchInput(_common.BaseModel):
    """Tool parameter kv match input."""

    instances: Optional[list[ToolParameterKVMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool parameter key value match instances.""",
    )
    metric_spec: Optional[ToolParameterKVMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool parameter key value match metric.""",
    )


class ToolParameterKVMatchInputDict(TypedDict, total=False):
    """Tool parameter kv match input."""

    instances: Optional[list[ToolParameterKVMatchInstanceDict]]
    """Required. Repeated tool parameter key value match instances."""

    metric_spec: Optional[ToolParameterKVMatchSpecDict]
    """Required. Spec for tool parameter key value match metric."""


ToolParameterKVMatchInputOrDict = Union[
    ToolParameterKVMatchInput, ToolParameterKVMatchInputDict
]


class AutoraterConfig(_common.BaseModel):
    """The configs for autorater."""

    autorater_model: Optional[str] = Field(
        default=None,
        description="""Optional. The fully qualified name of the publisher model or tuned autorater endpoint to use. Publisher model format: `projects/{project}/locations/{location}/publishers/*/models/*` Tuned model endpoint format: `projects/{project}/locations/{location}/endpoints/{endpoint}`""",
    )
    flip_enabled: Optional[bool] = Field(
        default=None,
        description="""Optional. Default is true. Whether to flip the candidate and baseline responses. This is only applicable to the pairwise metric. If enabled, also provide PairwiseMetricSpec.candidate_response_field_name and PairwiseMetricSpec.baseline_response_field_name. When rendering PairwiseMetricSpec.metric_prompt_template, the candidate and baseline fields will be flipped for half of the samples to reduce bias.""",
    )
    sampling_count: Optional[int] = Field(
        default=None,
        description="""Optional. Number of samples for each instance in the dataset. If not specified, the default is 4. Minimum value is 1, maximum value is 32.""",
    )


class AutoraterConfigDict(TypedDict, total=False):
    """The configs for autorater."""

    autorater_model: Optional[str]
    """Optional. The fully qualified name of the publisher model or tuned autorater endpoint to use. Publisher model format: `projects/{project}/locations/{location}/publishers/*/models/*` Tuned model endpoint format: `projects/{project}/locations/{location}/endpoints/{endpoint}`"""

    flip_enabled: Optional[bool]
    """Optional. Default is true. Whether to flip the candidate and baseline responses. This is only applicable to the pairwise metric. If enabled, also provide PairwiseMetricSpec.candidate_response_field_name and PairwiseMetricSpec.baseline_response_field_name. When rendering PairwiseMetricSpec.metric_prompt_template, the candidate and baseline fields will be flipped for half of the samples to reduce bias."""

    sampling_count: Optional[int]
    """Optional. Number of samples for each instance in the dataset. If not specified, the default is 4. Minimum value is 1, maximum value is 32."""


AutoraterConfigOrDict = Union[AutoraterConfig, AutoraterConfigDict]


class HttpOptions(_common.BaseModel):
    """HTTP options to be used in each of the requests."""

    base_url: Optional[str] = Field(
        default=None,
        description="""The base URL for the AI platform service endpoint.""",
    )
    api_version: Optional[str] = Field(
        default=None, description="""Specifies the version of the API to use."""
    )
    headers: Optional[dict[str, str]] = Field(
        default=None,
        description="""Additional HTTP headers to be sent with the request.""",
    )
    timeout: Optional[int] = Field(
        default=None, description="""Timeout for the request in milliseconds."""
    )
    client_args: Optional[dict[str, Any]] = Field(
        default=None, description="""Args passed to the HTTP client."""
    )
    async_client_args: Optional[dict[str, Any]] = Field(
        default=None, description="""Args passed to the async HTTP client."""
    )


class HttpOptionsDict(TypedDict, total=False):
    """HTTP options to be used in each of the requests."""

    base_url: Optional[str]
    """The base URL for the AI platform service endpoint."""

    api_version: Optional[str]
    """Specifies the version of the API to use."""

    headers: Optional[dict[str, str]]
    """Additional HTTP headers to be sent with the request."""

    timeout: Optional[int]
    """Timeout for the request in milliseconds."""

    client_args: Optional[dict[str, Any]]
    """Args passed to the HTTP client."""

    async_client_args: Optional[dict[str, Any]]
    """Args passed to the async HTTP client."""


HttpOptionsOrDict = Union[HttpOptions, HttpOptionsDict]


class EvaluateInstancesConfig(_common.BaseModel):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class EvaluateInstancesConfigDict(TypedDict, total=False):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


EvaluateInstancesConfigOrDict = Union[
    EvaluateInstancesConfig, EvaluateInstancesConfigDict
]


class _EvaluateInstancesRequestParameters(_common.BaseModel):
    """Parameters for evaluating instances."""

    bleu_input: Optional[BleuInput] = Field(default=None, description="""""")
    exact_match_input: Optional[ExactMatchInput] = Field(
        default=None, description=""""""
    )
    rouge_input: Optional[RougeInput] = Field(default=None, description="""""")
    pointwise_metric_input: Optional[PointwiseMetricInput] = Field(
        default=None, description=""""""
    )
    pairwise_metric_input: Optional[PairwiseMetricInput] = Field(
        default=None, description=""""""
    )
    tool_call_valid_input: Optional[ToolCallValidInput] = Field(
        default=None, description=""""""
    )
    tool_name_match_input: Optional[ToolNameMatchInput] = Field(
        default=None, description=""""""
    )
    tool_parameter_key_match_input: Optional[ToolParameterKeyMatchInput] = Field(
        default=None, description=""""""
    )
    tool_parameter_kv_match_input: Optional[ToolParameterKVMatchInput] = Field(
        default=None, description=""""""
    )
    autorater_config: Optional[AutoraterConfig] = Field(
        default=None, description=""""""
    )
    config: Optional[EvaluateInstancesConfig] = Field(default=None, description="""""")


class _EvaluateInstancesRequestParametersDict(TypedDict, total=False):
    """Parameters for evaluating instances."""

    bleu_input: Optional[BleuInputDict]
    """"""

    exact_match_input: Optional[ExactMatchInputDict]
    """"""

    rouge_input: Optional[RougeInputDict]
    """"""

    pointwise_metric_input: Optional[PointwiseMetricInputDict]
    """"""

    pairwise_metric_input: Optional[PairwiseMetricInputDict]
    """"""

    tool_call_valid_input: Optional[ToolCallValidInputDict]
    """"""

    tool_name_match_input: Optional[ToolNameMatchInputDict]
    """"""

    tool_parameter_key_match_input: Optional[ToolParameterKeyMatchInputDict]
    """"""

    tool_parameter_kv_match_input: Optional[ToolParameterKVMatchInputDict]
    """"""

    autorater_config: Optional[AutoraterConfigDict]
    """"""

    config: Optional[EvaluateInstancesConfigDict]
    """"""


_EvaluateInstancesRequestParametersOrDict = Union[
    _EvaluateInstancesRequestParameters, _EvaluateInstancesRequestParametersDict
]


class BleuMetricValue(_common.BaseModel):
    """Bleu metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Bleu score."""
    )


class BleuMetricValueDict(TypedDict, total=False):
    """Bleu metric value for an instance."""

    score: Optional[float]
    """Output only. Bleu score."""


BleuMetricValueOrDict = Union[BleuMetricValue, BleuMetricValueDict]


class BleuResults(_common.BaseModel):
    """Results for bleu metric."""

    bleu_metric_values: Optional[list[BleuMetricValue]] = Field(
        default=None, description="""Output only. Bleu metric values."""
    )


class BleuResultsDict(TypedDict, total=False):
    """Results for bleu metric."""

    bleu_metric_values: Optional[list[BleuMetricValueDict]]
    """Output only. Bleu metric values."""


BleuResultsOrDict = Union[BleuResults, BleuResultsDict]


class CometResult(_common.BaseModel):
    """Spec for Comet result - calculates the comet score for the given instance using the version specified in the spec."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Comet score. Range depends on version.""",
    )


class CometResultDict(TypedDict, total=False):
    """Spec for Comet result - calculates the comet score for the given instance using the version specified in the spec."""

    score: Optional[float]
    """Output only. Comet score. Range depends on version."""


CometResultOrDict = Union[CometResult, CometResultDict]


class ExactMatchMetricValue(_common.BaseModel):
    """Exact match metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Exact match score."""
    )


class ExactMatchMetricValueDict(TypedDict, total=False):
    """Exact match metric value for an instance."""

    score: Optional[float]
    """Output only. Exact match score."""


ExactMatchMetricValueOrDict = Union[ExactMatchMetricValue, ExactMatchMetricValueDict]


class ExactMatchResults(_common.BaseModel):
    """Results for exact match metric."""

    exact_match_metric_values: Optional[list[ExactMatchMetricValue]] = Field(
        default=None, description="""Output only. Exact match metric values."""
    )


class ExactMatchResultsDict(TypedDict, total=False):
    """Results for exact match metric."""

    exact_match_metric_values: Optional[list[ExactMatchMetricValueDict]]
    """Output only. Exact match metric values."""


ExactMatchResultsOrDict = Union[ExactMatchResults, ExactMatchResultsDict]


class MetricxResult(_common.BaseModel):
    """Spec for MetricX result - calculates the MetricX score for the given instance using the version specified in the spec."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. MetricX score. Range depends on version.""",
    )


class MetricxResultDict(TypedDict, total=False):
    """Spec for MetricX result - calculates the MetricX score for the given instance using the version specified in the spec."""

    score: Optional[float]
    """Output only. MetricX score. Range depends on version."""


MetricxResultOrDict = Union[MetricxResult, MetricxResultDict]


class RawOutput(_common.BaseModel):
    """Raw output."""

    raw_output: Optional[list[str]] = Field(
        default=None, description="""Output only. Raw output string."""
    )


class RawOutputDict(TypedDict, total=False):
    """Raw output."""

    raw_output: Optional[list[str]]
    """Output only. Raw output string."""


RawOutputOrDict = Union[RawOutput, RawOutputDict]


class CustomOutput(_common.BaseModel):
    """Spec for custom output."""

    raw_outputs: Optional[RawOutput] = Field(
        default=None, description="""Output only. List of raw output strings."""
    )


class CustomOutputDict(TypedDict, total=False):
    """Spec for custom output."""

    raw_outputs: Optional[RawOutputDict]
    """Output only. List of raw output strings."""


CustomOutputOrDict = Union[CustomOutput, CustomOutputDict]


class PairwiseMetricResult(_common.BaseModel):
    """Spec for pairwise metric result."""

    custom_output: Optional[CustomOutput] = Field(
        default=None, description="""Output only. Spec for custom output."""
    )
    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for pairwise metric score.""",
    )
    pairwise_choice: Optional[PairwiseChoice] = Field(
        default=None, description="""Output only. Pairwise metric choice."""
    )


class PairwiseMetricResultDict(TypedDict, total=False):
    """Spec for pairwise metric result."""

    custom_output: Optional[CustomOutputDict]
    """Output only. Spec for custom output."""

    explanation: Optional[str]
    """Output only. Explanation for pairwise metric score."""

    pairwise_choice: Optional[PairwiseChoice]
    """Output only. Pairwise metric choice."""


PairwiseMetricResultOrDict = Union[PairwiseMetricResult, PairwiseMetricResultDict]


class PointwiseMetricResult(_common.BaseModel):
    """Spec for pointwise metric result."""

    custom_output: Optional[CustomOutput] = Field(
        default=None, description="""Output only. Spec for custom output."""
    )
    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for pointwise metric score.""",
    )
    score: Optional[float] = Field(
        default=None, description="""Output only. Pointwise metric score."""
    )


class PointwiseMetricResultDict(TypedDict, total=False):
    """Spec for pointwise metric result."""

    custom_output: Optional[CustomOutputDict]
    """Output only. Spec for custom output."""

    explanation: Optional[str]
    """Output only. Explanation for pointwise metric score."""

    score: Optional[float]
    """Output only. Pointwise metric score."""


PointwiseMetricResultOrDict = Union[PointwiseMetricResult, PointwiseMetricResultDict]


class RougeMetricValue(_common.BaseModel):
    """Rouge metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Rouge score."""
    )


class RougeMetricValueDict(TypedDict, total=False):
    """Rouge metric value for an instance."""

    score: Optional[float]
    """Output only. Rouge score."""


RougeMetricValueOrDict = Union[RougeMetricValue, RougeMetricValueDict]


class RougeResults(_common.BaseModel):
    """Results for rouge metric."""

    rouge_metric_values: Optional[list[RougeMetricValue]] = Field(
        default=None, description="""Output only. Rouge metric values."""
    )


class RougeResultsDict(TypedDict, total=False):
    """Results for rouge metric."""

    rouge_metric_values: Optional[list[RougeMetricValueDict]]
    """Output only. Rouge metric values."""


RougeResultsOrDict = Union[RougeResults, RougeResultsDict]


class RubricCritiqueResult(_common.BaseModel):
    """Rubric critique result."""

    rubric: Optional[str] = Field(
        default=None, description="""Output only. Rubric to be evaluated."""
    )
    verdict: Optional[bool] = Field(
        default=None,
        description="""Output only. Verdict for the rubric - true if the rubric is met, false otherwise.""",
    )


class RubricCritiqueResultDict(TypedDict, total=False):
    """Rubric critique result."""

    rubric: Optional[str]
    """Output only. Rubric to be evaluated."""

    verdict: Optional[bool]
    """Output only. Verdict for the rubric - true if the rubric is met, false otherwise."""


RubricCritiqueResultOrDict = Union[RubricCritiqueResult, RubricCritiqueResultDict]


class RubricBasedInstructionFollowingResult(_common.BaseModel):
    """Result for RubricBasedInstructionFollowing metric."""

    rubric_critique_results: Optional[list[RubricCritiqueResult]] = Field(
        default=None,
        description="""Output only. List of per rubric critique results.""",
    )
    score: Optional[float] = Field(
        default=None,
        description="""Output only. Overall score for the instruction following.""",
    )


class RubricBasedInstructionFollowingResultDict(TypedDict, total=False):
    """Result for RubricBasedInstructionFollowing metric."""

    rubric_critique_results: Optional[list[RubricCritiqueResultDict]]
    """Output only. List of per rubric critique results."""

    score: Optional[float]
    """Output only. Overall score for the instruction following."""


RubricBasedInstructionFollowingResultOrDict = Union[
    RubricBasedInstructionFollowingResult,
    RubricBasedInstructionFollowingResultDict,
]


class SummarizationVerbosityResult(_common.BaseModel):
    """Spec for summarization verbosity result."""

    confidence: Optional[float] = Field(
        default=None,
        description="""Output only. Confidence for summarization verbosity score.""",
    )
    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for summarization verbosity score.""",
    )
    score: Optional[float] = Field(
        default=None,
        description="""Output only. Summarization Verbosity score.""",
    )


class SummarizationVerbosityResultDict(TypedDict, total=False):
    """Spec for summarization verbosity result."""

    confidence: Optional[float]
    """Output only. Confidence for summarization verbosity score."""

    explanation: Optional[str]
    """Output only. Explanation for summarization verbosity score."""

    score: Optional[float]
    """Output only. Summarization Verbosity score."""


SummarizationVerbosityResultOrDict = Union[
    SummarizationVerbosityResult, SummarizationVerbosityResultDict
]


class ToolCallValidMetricValue(_common.BaseModel):
    """Tool call valid metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Tool call valid score."""
    )


class ToolCallValidMetricValueDict(TypedDict, total=False):
    """Tool call valid metric value for an instance."""

    score: Optional[float]
    """Output only. Tool call valid score."""


ToolCallValidMetricValueOrDict = Union[
    ToolCallValidMetricValue, ToolCallValidMetricValueDict
]


class ToolCallValidResults(_common.BaseModel):
    """Results for tool call valid metric."""

    tool_call_valid_metric_values: Optional[list[ToolCallValidMetricValue]] = Field(
        default=None,
        description="""Output only. Tool call valid metric values.""",
    )


class ToolCallValidResultsDict(TypedDict, total=False):
    """Results for tool call valid metric."""

    tool_call_valid_metric_values: Optional[list[ToolCallValidMetricValueDict]]
    """Output only. Tool call valid metric values."""


ToolCallValidResultsOrDict = Union[ToolCallValidResults, ToolCallValidResultsDict]


class ToolNameMatchMetricValue(_common.BaseModel):
    """Tool name match metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Tool name match score."""
    )


class ToolNameMatchMetricValueDict(TypedDict, total=False):
    """Tool name match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool name match score."""


ToolNameMatchMetricValueOrDict = Union[
    ToolNameMatchMetricValue, ToolNameMatchMetricValueDict
]


class ToolNameMatchResults(_common.BaseModel):
    """Results for tool name match metric."""

    tool_name_match_metric_values: Optional[list[ToolNameMatchMetricValue]] = Field(
        default=None,
        description="""Output only. Tool name match metric values.""",
    )


class ToolNameMatchResultsDict(TypedDict, total=False):
    """Results for tool name match metric."""

    tool_name_match_metric_values: Optional[list[ToolNameMatchMetricValueDict]]
    """Output only. Tool name match metric values."""


ToolNameMatchResultsOrDict = Union[ToolNameMatchResults, ToolNameMatchResultsDict]


class ToolParameterKeyMatchMetricValue(_common.BaseModel):
    """Tool parameter key match metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Tool parameter key match score.""",
    )


class ToolParameterKeyMatchMetricValueDict(TypedDict, total=False):
    """Tool parameter key match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool parameter key match score."""


ToolParameterKeyMatchMetricValueOrDict = Union[
    ToolParameterKeyMatchMetricValue, ToolParameterKeyMatchMetricValueDict
]


class ToolParameterKeyMatchResults(_common.BaseModel):
    """Results for tool parameter key match metric."""

    tool_parameter_key_match_metric_values: Optional[
        list[ToolParameterKeyMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. Tool parameter key match metric values.""",
    )


class ToolParameterKeyMatchResultsDict(TypedDict, total=False):
    """Results for tool parameter key match metric."""

    tool_parameter_key_match_metric_values: Optional[
        list[ToolParameterKeyMatchMetricValueDict]
    ]
    """Output only. Tool parameter key match metric values."""


ToolParameterKeyMatchResultsOrDict = Union[
    ToolParameterKeyMatchResults, ToolParameterKeyMatchResultsDict
]


class ToolParameterKVMatchMetricValue(_common.BaseModel):
    """Tool parameter key value match metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Tool parameter key value match score.""",
    )


class ToolParameterKVMatchMetricValueDict(TypedDict, total=False):
    """Tool parameter key value match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool parameter key value match score."""


ToolParameterKVMatchMetricValueOrDict = Union[
    ToolParameterKVMatchMetricValue, ToolParameterKVMatchMetricValueDict
]


class ToolParameterKVMatchResults(_common.BaseModel):
    """Results for tool parameter key value match metric."""

    tool_parameter_kv_match_metric_values: Optional[
        list[ToolParameterKVMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. Tool parameter key value match metric values.""",
    )


class ToolParameterKVMatchResultsDict(TypedDict, total=False):
    """Results for tool parameter key value match metric."""

    tool_parameter_kv_match_metric_values: Optional[
        list[ToolParameterKVMatchMetricValueDict]
    ]
    """Output only. Tool parameter key value match metric values."""


ToolParameterKVMatchResultsOrDict = Union[
    ToolParameterKVMatchResults, ToolParameterKVMatchResultsDict
]


class TrajectoryAnyOrderMatchMetricValue(_common.BaseModel):
    """TrajectoryAnyOrderMatch metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. TrajectoryAnyOrderMatch score.""",
    )


class TrajectoryAnyOrderMatchMetricValueDict(TypedDict, total=False):
    """TrajectoryAnyOrderMatch metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryAnyOrderMatch score."""


TrajectoryAnyOrderMatchMetricValueOrDict = Union[
    TrajectoryAnyOrderMatchMetricValue, TrajectoryAnyOrderMatchMetricValueDict
]


class TrajectoryAnyOrderMatchResults(_common.BaseModel):
    """Results for TrajectoryAnyOrderMatch metric."""

    trajectory_any_order_match_metric_values: Optional[
        list[TrajectoryAnyOrderMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryAnyOrderMatch metric values.""",
    )


class TrajectoryAnyOrderMatchResultsDict(TypedDict, total=False):
    """Results for TrajectoryAnyOrderMatch metric."""

    trajectory_any_order_match_metric_values: Optional[
        list[TrajectoryAnyOrderMatchMetricValueDict]
    ]
    """Output only. TrajectoryAnyOrderMatch metric values."""


TrajectoryAnyOrderMatchResultsOrDict = Union[
    TrajectoryAnyOrderMatchResults, TrajectoryAnyOrderMatchResultsDict
]


class TrajectoryExactMatchMetricValue(_common.BaseModel):
    """TrajectoryExactMatch metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. TrajectoryExactMatch score."""
    )


class TrajectoryExactMatchMetricValueDict(TypedDict, total=False):
    """TrajectoryExactMatch metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryExactMatch score."""


TrajectoryExactMatchMetricValueOrDict = Union[
    TrajectoryExactMatchMetricValue, TrajectoryExactMatchMetricValueDict
]


class TrajectoryExactMatchResults(_common.BaseModel):
    """Results for TrajectoryExactMatch metric."""

    trajectory_exact_match_metric_values: Optional[
        list[TrajectoryExactMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryExactMatch metric values.""",
    )


class TrajectoryExactMatchResultsDict(TypedDict, total=False):
    """Results for TrajectoryExactMatch metric."""

    trajectory_exact_match_metric_values: Optional[
        list[TrajectoryExactMatchMetricValueDict]
    ]
    """Output only. TrajectoryExactMatch metric values."""


TrajectoryExactMatchResultsOrDict = Union[
    TrajectoryExactMatchResults, TrajectoryExactMatchResultsDict
]


class TrajectoryInOrderMatchMetricValue(_common.BaseModel):
    """TrajectoryInOrderMatch metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. TrajectoryInOrderMatch score.""",
    )


class TrajectoryInOrderMatchMetricValueDict(TypedDict, total=False):
    """TrajectoryInOrderMatch metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryInOrderMatch score."""


TrajectoryInOrderMatchMetricValueOrDict = Union[
    TrajectoryInOrderMatchMetricValue, TrajectoryInOrderMatchMetricValueDict
]


class TrajectoryInOrderMatchResults(_common.BaseModel):
    """Results for TrajectoryInOrderMatch metric."""

    trajectory_in_order_match_metric_values: Optional[
        list[TrajectoryInOrderMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryInOrderMatch metric values.""",
    )


class TrajectoryInOrderMatchResultsDict(TypedDict, total=False):
    """Results for TrajectoryInOrderMatch metric."""

    trajectory_in_order_match_metric_values: Optional[
        list[TrajectoryInOrderMatchMetricValueDict]
    ]
    """Output only. TrajectoryInOrderMatch metric values."""


TrajectoryInOrderMatchResultsOrDict = Union[
    TrajectoryInOrderMatchResults, TrajectoryInOrderMatchResultsDict
]


class TrajectoryPrecisionMetricValue(_common.BaseModel):
    """TrajectoryPrecision metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. TrajectoryPrecision score."""
    )


class TrajectoryPrecisionMetricValueDict(TypedDict, total=False):
    """TrajectoryPrecision metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryPrecision score."""


TrajectoryPrecisionMetricValueOrDict = Union[
    TrajectoryPrecisionMetricValue, TrajectoryPrecisionMetricValueDict
]


class TrajectoryPrecisionResults(_common.BaseModel):
    """Results for TrajectoryPrecision metric."""

    trajectory_precision_metric_values: Optional[
        list[TrajectoryPrecisionMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryPrecision metric values.""",
    )


class TrajectoryPrecisionResultsDict(TypedDict, total=False):
    """Results for TrajectoryPrecision metric."""

    trajectory_precision_metric_values: Optional[
        list[TrajectoryPrecisionMetricValueDict]
    ]
    """Output only. TrajectoryPrecision metric values."""


TrajectoryPrecisionResultsOrDict = Union[
    TrajectoryPrecisionResults, TrajectoryPrecisionResultsDict
]


class TrajectoryRecallMetricValue(_common.BaseModel):
    """TrajectoryRecall metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. TrajectoryRecall score."""
    )


class TrajectoryRecallMetricValueDict(TypedDict, total=False):
    """TrajectoryRecall metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectoryRecall score."""


TrajectoryRecallMetricValueOrDict = Union[
    TrajectoryRecallMetricValue, TrajectoryRecallMetricValueDict
]


class TrajectoryRecallResults(_common.BaseModel):
    """Results for TrajectoryRecall metric."""

    trajectory_recall_metric_values: Optional[
        list[TrajectoryRecallMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectoryRecall metric values.""",
    )


class TrajectoryRecallResultsDict(TypedDict, total=False):
    """Results for TrajectoryRecall metric."""

    trajectory_recall_metric_values: Optional[list[TrajectoryRecallMetricValueDict]]
    """Output only. TrajectoryRecall metric values."""


TrajectoryRecallResultsOrDict = Union[
    TrajectoryRecallResults, TrajectoryRecallResultsDict
]


class TrajectorySingleToolUseMetricValue(_common.BaseModel):
    """TrajectorySingleToolUse metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. TrajectorySingleToolUse score.""",
    )


class TrajectorySingleToolUseMetricValueDict(TypedDict, total=False):
    """TrajectorySingleToolUse metric value for an instance."""

    score: Optional[float]
    """Output only. TrajectorySingleToolUse score."""


TrajectorySingleToolUseMetricValueOrDict = Union[
    TrajectorySingleToolUseMetricValue, TrajectorySingleToolUseMetricValueDict
]


class TrajectorySingleToolUseResults(_common.BaseModel):
    """Results for TrajectorySingleToolUse metric."""

    trajectory_single_tool_use_metric_values: Optional[
        list[TrajectorySingleToolUseMetricValue]
    ] = Field(
        default=None,
        description="""Output only. TrajectorySingleToolUse metric values.""",
    )


class TrajectorySingleToolUseResultsDict(TypedDict, total=False):
    """Results for TrajectorySingleToolUse metric."""

    trajectory_single_tool_use_metric_values: Optional[
        list[TrajectorySingleToolUseMetricValueDict]
    ]
    """Output only. TrajectorySingleToolUse metric values."""


TrajectorySingleToolUseResultsOrDict = Union[
    TrajectorySingleToolUseResults, TrajectorySingleToolUseResultsDict
]


class EvaluateInstancesResponse(_common.BaseModel):
    """Result of evaluating an LLM metric."""

    bleu_results: Optional[BleuResults] = Field(
        default=None, description="""Results for bleu metric."""
    )
    comet_result: Optional[CometResult] = Field(
        default=None,
        description="""Translation metrics. Result for Comet metric.""",
    )
    exact_match_results: Optional[ExactMatchResults] = Field(
        default=None,
        description="""Auto metric evaluation results. Results for exact match metric.""",
    )
    metricx_result: Optional[MetricxResult] = Field(
        default=None, description="""Result for Metricx metric."""
    )
    pairwise_metric_result: Optional[PairwiseMetricResult] = Field(
        default=None, description="""Result for pairwise metric."""
    )
    pointwise_metric_result: Optional[PointwiseMetricResult] = Field(
        default=None,
        description="""Generic metrics. Result for pointwise metric.""",
    )
    rouge_results: Optional[RougeResults] = Field(
        default=None, description="""Results for rouge metric."""
    )
    rubric_based_instruction_following_result: Optional[
        RubricBasedInstructionFollowingResult
    ] = Field(
        default=None,
        description="""Result for rubric based instruction following metric.""",
    )
    summarization_verbosity_result: Optional[SummarizationVerbosityResult] = Field(
        default=None,
        description="""Result for summarization verbosity metric.""",
    )
    tool_call_valid_results: Optional[ToolCallValidResults] = Field(
        default=None,
        description="""Tool call metrics. Results for tool call valid metric.""",
    )
    tool_name_match_results: Optional[ToolNameMatchResults] = Field(
        default=None, description="""Results for tool name match metric."""
    )
    tool_parameter_key_match_results: Optional[ToolParameterKeyMatchResults] = Field(
        default=None,
        description="""Results for tool parameter key match metric.""",
    )
    tool_parameter_kv_match_results: Optional[ToolParameterKVMatchResults] = Field(
        default=None,
        description="""Results for tool parameter key value match metric.""",
    )
    trajectory_any_order_match_results: Optional[
        TrajectoryAnyOrderMatchResults
    ] = Field(
        default=None,
        description="""Result for trajectory any order match metric.""",
    )
    trajectory_exact_match_results: Optional[TrajectoryExactMatchResults] = Field(
        default=None,
        description="""Result for trajectory exact match metric.""",
    )
    trajectory_in_order_match_results: Optional[TrajectoryInOrderMatchResults] = Field(
        default=None,
        description="""Result for trajectory in order match metric.""",
    )
    trajectory_precision_results: Optional[TrajectoryPrecisionResults] = Field(
        default=None, description="""Result for trajectory precision metric."""
    )
    trajectory_recall_results: Optional[TrajectoryRecallResults] = Field(
        default=None, description="""Results for trajectory recall metric."""
    )
    trajectory_single_tool_use_results: Optional[
        TrajectorySingleToolUseResults
    ] = Field(
        default=None,
        description="""Results for trajectory single tool use metric.""",
    )


class EvaluateInstancesResponseDict(TypedDict, total=False):
    """Result of evaluating an LLM metric."""

    bleu_results: Optional[BleuResultsDict]
    """Results for bleu metric."""

    comet_result: Optional[CometResultDict]
    """Translation metrics. Result for Comet metric."""

    exact_match_results: Optional[ExactMatchResultsDict]
    """Auto metric evaluation results. Results for exact match metric."""

    metricx_result: Optional[MetricxResultDict]
    """Result for Metricx metric."""

    pairwise_metric_result: Optional[PairwiseMetricResultDict]
    """Result for pairwise metric."""

    pointwise_metric_result: Optional[PointwiseMetricResultDict]
    """Generic metrics. Result for pointwise metric."""

    rouge_results: Optional[RougeResultsDict]
    """Results for rouge metric."""

    rubric_based_instruction_following_result: Optional[
        RubricBasedInstructionFollowingResultDict
    ]
    """Result for rubric based instruction following metric."""

    summarization_verbosity_result: Optional[SummarizationVerbosityResultDict]
    """Result for summarization verbosity metric."""

    tool_call_valid_results: Optional[ToolCallValidResultsDict]
    """Tool call metrics. Results for tool call valid metric."""

    tool_name_match_results: Optional[ToolNameMatchResultsDict]
    """Results for tool name match metric."""

    tool_parameter_key_match_results: Optional[ToolParameterKeyMatchResultsDict]
    """Results for tool parameter key match metric."""

    tool_parameter_kv_match_results: Optional[ToolParameterKVMatchResultsDict]
    """Results for tool parameter key value match metric."""

    trajectory_any_order_match_results: Optional[TrajectoryAnyOrderMatchResultsDict]
    """Result for trajectory any order match metric."""

    trajectory_exact_match_results: Optional[TrajectoryExactMatchResultsDict]
    """Result for trajectory exact match metric."""

    trajectory_in_order_match_results: Optional[TrajectoryInOrderMatchResultsDict]
    """Result for trajectory in order match metric."""

    trajectory_precision_results: Optional[TrajectoryPrecisionResultsDict]
    """Result for trajectory precision metric."""

    trajectory_recall_results: Optional[TrajectoryRecallResultsDict]
    """Results for trajectory recall metric."""

    trajectory_single_tool_use_results: Optional[TrajectorySingleToolUseResultsDict]
    """Results for trajectory single tool use metric."""


EvaluateInstancesResponseOrDict = Union[
    EvaluateInstancesResponse, EvaluateInstancesResponseDict
]


class Message(_common.BaseModel):
    """Represents a single message turn in a conversation."""

    turn_id: Optional[str] = Field(
        default=None, description="""Unique identifier for the message turn."""
    )
    content: Optional[genai_types.Content] = Field(
        default=None,
        description="""Content of the message, including function call.""",
    )
    creation_timestamp: Optional[datetime.datetime] = Field(
        default=None,
        description="""Timestamp indicating when the message was created.""",
    )
    author: Optional[str] = Field(
        default=None,
        description="""Name of the entity that produced the message.""",
    )


class MessageDict(TypedDict, total=False):
    """Represents a single message turn in a conversation."""

    turn_id: Optional[str]
    """Unique identifier for the message turn."""

    content: Optional[genai_types.Content]
    """Content of the message, including function call."""

    creation_timestamp: Optional[datetime.datetime]
    """Timestamp indicating when the message was created."""

    author: Optional[str]
    """Name of the entity that produced the message."""


MessageOrDict = Union[Message, MessageDict]


class AgentData(_common.BaseModel):
    """Container for all agent-specific data."""

    tool_use_trajectory: Optional[list[Message]] = Field(
        default=None,
        description="""Tool use trajectory in chronological order.""",
    )
    intermediate_responses: Optional[list[Message]] = Field(
        default=None,
        description="""Intermediate responses generated by sub-agents to convey progress or status in a multi-agent system, distinct from the final response.""",
    )


class AgentDataDict(TypedDict, total=False):
    """Container for all agent-specific data."""

    tool_use_trajectory: Optional[list[MessageDict]]
    """Tool use trajectory in chronological order."""

    intermediate_responses: Optional[list[MessageDict]]
    """Intermediate responses generated by sub-agents to convey progress or status in a multi-agent system, distinct from the final response."""


AgentDataOrDict = Union[AgentData, AgentDataDict]


class ResponseCandidate(_common.BaseModel):
    """A model-generated content to the prompt."""

    response: Optional[genai_types.Content] = Field(
        default=None,
        description="""The final model-generated response to the `prompt`.""",
    )
    agent_data: Optional[AgentData] = Field(
        default=None,
        description="""Agent-specific data including tool use trajectory and intermediate responses for more complex interactions.""",
    )


class ResponseCandidateDict(TypedDict, total=False):
    """A model-generated content to the prompt."""

    response: Optional[genai_types.Content]
    """The final model-generated response to the `prompt`."""

    agent_data: Optional[AgentDataDict]
    """Agent-specific data including tool use trajectory and intermediate responses for more complex interactions."""


ResponseCandidateOrDict = Union[ResponseCandidate, ResponseCandidateDict]


class EvalCase(_common.BaseModel):
    """A comprehensive representation of a GenAI interaction for evaluation."""

    prompt: Optional[genai_types.Content] = Field(
        default=None,
        description="""The most recent user message (current input).""",
    )
    responses: Optional[list[ResponseCandidate]] = Field(
        default=None,
        description="""Model-generated replies to the last user message. Multiple responses are allowed to support use cases such as comparing different model outputs.""",
    )
    reference: Optional[ResponseCandidate] = Field(
        default=None,
        description="""User-provided, golden reference model reply to prompt in context of chat history.""",
    )
    system_instruction: Optional[genai_types.Content] = Field(
        default=None, description="""System instruction for the model."""
    )
    conversation_history: Optional[list[Message]] = Field(
        default=None,
        description="""List of all prior messages in the conversation (chat history).""",
    )
    eval_case_id: Optional[str] = Field(
        default=None,
        description="""Unique identifier for the evaluation case.""",
    )
    # Allow extra fields to support custom metric prompts and stay backward compatible.
    model_config = ConfigDict(frozen=True, extra="allow")


class EvalCaseDict(TypedDict, total=False):
    """A comprehensive representation of a GenAI interaction for evaluation."""

    prompt: Optional[genai_types.Content]
    """The most recent user message (current input)."""

    responses: Optional[list[ResponseCandidateDict]]
    """Model-generated replies to the last user message. Multiple responses are allowed to support use cases such as comparing different model outputs."""

    reference: Optional[ResponseCandidateDict]
    """User-provided, golden reference model reply to prompt in context of chat history."""

    system_instruction: Optional[genai_types.Content]
    """System instruction for the model."""

    conversation_history: Optional[list[MessageDict]]
    """List of all prior messages in the conversation (chat history)."""

    eval_case_id: Optional[str]
    """Unique identifier for the evaluation case."""


EvalCaseOrDict = Union[EvalCase, EvalCaseDict]


class GcsSource(_common.BaseModel):
    """The Google Cloud Storage location for the input content."""

    uris: Optional[list[str]] = Field(
        default=None,
        description="""Required. Google Cloud Storage URI(-s) to the input file(s). May contain wildcards. For more information on wildcards, see https://cloud.google.com/storage/docs/wildcards.""",
    )


class GcsSourceDict(TypedDict, total=False):
    """The Google Cloud Storage location for the input content."""

    uris: Optional[list[str]]
    """Required. Google Cloud Storage URI(-s) to the input file(s). May contain wildcards. For more information on wildcards, see https://cloud.google.com/storage/docs/wildcards."""


GcsSourceOrDict = Union[GcsSource, GcsSourceDict]


class BigQuerySource(_common.BaseModel):
    """The BigQuery location for the input content."""

    input_uri: Optional[str] = Field(
        default=None,
        description="""Required. BigQuery URI to a table, up to 2000 characters long. Accepted forms: * BigQuery path. For example: `bq://projectId.bqDatasetId.bqTableId`.""",
    )


class BigQuerySourceDict(TypedDict, total=False):
    """The BigQuery location for the input content."""

    input_uri: Optional[str]
    """Required. BigQuery URI to a table, up to 2000 characters long. Accepted forms: * BigQuery path. For example: `bq://projectId.bqDatasetId.bqTableId`."""


BigQuerySourceOrDict = Union[BigQuerySource, BigQuerySourceDict]


class EvaluationDataset(_common.BaseModel):
    """The dataset used for evaluation."""

    eval_cases: Optional[list[EvalCase]] = Field(
        default=None, description="""The evaluation cases to be evaluated."""
    )
    eval_dataset_df: Optional[pd.DataFrame] = Field(
        default=None,
        description="""The evaluation dataset in the form of a Pandas DataFrame.""",
    )
    gcs_source: Optional[GcsSource] = Field(
        default=None,
        description="""The GCS source for the evaluation dataset.""",
    )
    bigquery_source: Optional[BigQuerySource] = Field(
        default=None,
        description="""The BigQuery source for the evaluation dataset.""",
    )


class EvaluationDatasetDict(TypedDict, total=False):
    """The dataset used for evaluation."""

    eval_cases: Optional[list[EvalCaseDict]]
    """The evaluation cases to be evaluated."""

    eval_dataset_df: Optional[pd.DataFrame]
    """The evaluation dataset in the form of a Pandas DataFrame."""

    gcs_source: Optional[GcsSourceDict]
    """The GCS source for the evaluation dataset."""

    bigquery_source: Optional[BigQuerySourceDict]
    """The BigQuery source for the evaluation dataset."""


EvaluationDatasetOrDict = Union[EvaluationDataset, EvaluationDatasetDict]


class Metric(_common.BaseModel):
    """The metric used for evaluation."""

    name: Optional[str] = Field(default=None, description="""The name of the metric.""")
    custom_function: Optional[Callable] = Field(
        default=None,
        description="""The custom function that defines the end-to-end logic for metric computation.""",
    )
    prompt_template: Optional[str] = Field(
        default=None, description="""The prompt template for the metric."""
    )
    judge_model: Optional[str] = Field(
        default=None, description="""The judge model for the metric."""
    )
    judge_model_sampling_count: Optional[int] = Field(
        default=None, description="""The sampling count for the judge model."""
    )
    judge_model_system_instruction: Optional[str] = Field(
        default=None,
        description="""The system instruction for the judge model.""",
    )
    return_raw_output: Optional[bool] = Field(
        default=None,
        description="""Whether to return the raw output from the judge model.""",
    )
    parse_and_reduce_fn: Optional[Callable] = Field(
        default=None,
        description="""The parse and reduce function for the judge model.""",
    )
    aggregate_summary_fn: Optional[Callable] = Field(
        default=None,
        description="""The aggregate summary function for the judge model.""",
    )

    # Allow extra fields to support metric-specific config fields.
    model_config = ConfigDict(extra="allow")

    _is_predefined: bool = PrivateAttr(default=False)
    """A boolean indicating whether the metric is predefined."""

    _config_source: Optional[str] = PrivateAttr(default=None)
    """An optional string indicating the source of the metric configuration."""

    _version: Optional[str] = PrivateAttr(default=None)
    """An optional string indicating the version of the metric."""

    @model_validator(mode="after")
    @classmethod
    def validate_name(cls, model: "Metric") -> "Metric":
        if not model.name:
            raise ValueError("Metric name cannot be empty.")
        model.name = model.name.lower()
        return model

    def to_yaml_file(self, file_path: str, version: Optional[str] = None) -> None:
        """Dumps the metric object to a YAML file.

        Args:
            file_path: The path to the YAML file.
            version: Optional version string to include in the YAML output.

        Raises:
            ImportError: If the pyyaml library is not installed.
        """
        if yaml is None:
            raise ImportError(
                "YAML serialization requires the pyyaml library. Please install"
                " it using 'pip install google-cloud-aiplatform[evaluation]'."
            )

        fields_to_exclude_callables = set()
        for field_name, field_info in self.model_fields.items():
            annotation = field_info.annotation
            origin = typing.get_origin(annotation)

            is_field_callable_type = False
            if annotation is Callable or origin is Callable:
                is_field_callable_type = True
            elif origin is Union:
                args = typing.get_args(annotation)
                if any(
                    arg is Callable or typing.get_origin(arg) is Callable
                    for arg in args
                ):
                    is_field_callable_type = True

            if is_field_callable_type:
                fields_to_exclude_callables.add(field_name)

        data_to_dump = self.model_dump(
            exclude_unset=True,
            exclude_none=True,
            mode="json",
            exclude=fields_to_exclude_callables
            if fields_to_exclude_callables
            else None,
        )

        if version:
            data_to_dump["version"] = version

        with open(file_path, "w", encoding="utf-8") as f:
            yaml.dump(data_to_dump, f, sort_keys=False, allow_unicode=True)


class LLMMetric(Metric):
    """A metric that uses LLM-as-a-judge for evaluation."""

    @field_validator("prompt_template", mode="before")
    @classmethod
    def validate_prompt_template(cls, value: Union[str, "MetricPromptBuilder"]) -> str:
        """Validates prompt template to be a non-empty string."""
        if value is None:
            raise ValueError("Prompt template cannot be empty.")
        if isinstance(value, MetricPromptBuilder):
            value = str(value)
        if not value.strip():
            raise ValueError("Prompt template cannot be an empty string.")
        return value

    @field_validator("judge_model_sampling_count")
    @classmethod
    def validate_judge_model_sampling_count(cls, value: Optional[int]) -> Optional[int]:
        """Validates judge_model_sampling_count to be between 1 and 32."""
        if value is not None and (value < 1 or value > 32):
            raise ValueError("judge_model_sampling_count must be between 1 and 32.")
        return value


class MetricDict(TypedDict, total=False):
    """The metric used for evaluation."""

    name: Optional[str]
    """The name of the metric."""

    custom_function: Optional[Callable]
    """The custom function that defines the end-to-end logic for metric computation."""

    prompt_template: Optional[str]
    """The prompt template for the metric."""

    judge_model: Optional[str]
    """The judge model for the metric."""

    judge_model_sampling_count: Optional[int]
    """The sampling count for the judge model."""

    judge_model_system_instruction: Optional[str]
    """The system instruction for the judge model."""

    return_raw_output: Optional[bool]
    """Whether to return the raw output from the judge model."""

    parse_and_reduce_fn: Optional[Callable]
    """The parse and reduce function for the judge model."""

    aggregate_summary_fn: Optional[Callable]
    """The aggregate summary function for the judge model."""


MetricOrDict = Union[Metric, MetricDict]


class GcsDestination(_common.BaseModel):
    """The Google Cloud Storage location where the output is to be written to."""

    output_uri_prefix: Optional[str] = Field(
        default=None,
        description="""Required. Google Cloud Storage URI to output directory. If the uri doesn't end with '/', a '/' will be automatically appended. The directory is created if it doesn't exist.""",
    )


class GcsDestinationDict(TypedDict, total=False):
    """The Google Cloud Storage location where the output is to be written to."""

    output_uri_prefix: Optional[str]
    """Required. Google Cloud Storage URI to output directory. If the uri doesn't end with '/', a '/' will be automatically appended. The directory is created if it doesn't exist."""


GcsDestinationOrDict = Union[GcsDestination, GcsDestinationDict]


class OutputConfig(_common.BaseModel):
    """Config for evaluation output."""

    gcs_destination: Optional[GcsDestination] = Field(
        default=None,
        description="""Cloud storage destination for evaluation output.""",
    )


class OutputConfigDict(TypedDict, total=False):
    """Config for evaluation output."""

    gcs_destination: Optional[GcsDestinationDict]
    """Cloud storage destination for evaluation output."""


OutputConfigOrDict = Union[OutputConfig, OutputConfigDict]


class EvaluateDatasetConfig(_common.BaseModel):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class EvaluateDatasetConfigDict(TypedDict, total=False):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


EvaluateDatasetConfigOrDict = Union[EvaluateDatasetConfig, EvaluateDatasetConfigDict]


class _EvaluateDatasetRequestParameters(_common.BaseModel):
    """Parameters for batch dataset evaluation."""

    dataset: Optional[EvaluationDataset] = Field(default=None, description="""""")
    metrics: Optional[list[Metric]] = Field(default=None, description="""""")
    output_config: Optional[OutputConfig] = Field(default=None, description="""""")
    autorater_config: Optional[AutoraterConfig] = Field(
        default=None, description=""""""
    )
    config: Optional[EvaluateDatasetConfig] = Field(default=None, description="""""")


class _EvaluateDatasetRequestParametersDict(TypedDict, total=False):
    """Parameters for batch dataset evaluation."""

    dataset: Optional[EvaluationDatasetDict]
    """"""

    metrics: Optional[list[MetricDict]]
    """"""

    output_config: Optional[OutputConfigDict]
    """"""

    autorater_config: Optional[AutoraterConfigDict]
    """"""

    config: Optional[EvaluateDatasetConfigDict]
    """"""


_EvaluateDatasetRequestParametersOrDict = Union[
    _EvaluateDatasetRequestParameters, _EvaluateDatasetRequestParametersDict
]


class EvaluateDatasetOperation(_common.BaseModel):

    name: Optional[str] = Field(
        default=None,
        description="""The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.""",
    )
    metadata: Optional[dict[str, Any]] = Field(
        default=None,
        description="""Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any.""",
    )
    done: Optional[bool] = Field(
        default=None,
        description="""If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.""",
    )
    error: Optional[dict[str, Any]] = Field(
        default=None,
        description="""The error result of the operation in case of failure or cancellation.""",
    )
    response: Optional[EvaluationDataset] = Field(default=None, description="""""")


class EvaluateDatasetOperationDict(TypedDict, total=False):

    name: Optional[str]
    """The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`."""

    metadata: Optional[dict[str, Any]]
    """Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata.  Any method that returns a long-running operation should document the metadata type, if any."""

    done: Optional[bool]
    """If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available."""

    error: Optional[dict[str, Any]]
    """The error result of the operation in case of failure or cancellation."""

    response: Optional[EvaluationDatasetDict]
    """"""


EvaluateDatasetOperationOrDict = Union[
    EvaluateDatasetOperation, EvaluateDatasetOperationDict
]


class PromptTemplate(_common.BaseModel):
    """A prompt template for creating prompts with variables."""

    text: Optional[str] = Field(
        default=None, description="""The prompt template text."""
    )
    _VARIABLE_NAME_REGEX: ClassVar[str] = r"\{([_a-zA-Z][_a-zA-Z0-9]*)\}"

    @field_validator("text")
    @classmethod
    def text_must_not_be_empty(cls, value: str) -> str:
        if not value.strip():
            raise ValueError(
                "Prompt template text cannot be empty or consist only of" " whitespace."
            )
        return value

    @computed_field
    @property
    def variables(self) -> set[str]:
        return set(re.findall(self._VARIABLE_NAME_REGEX, self.text))

    def _split_template_by_variables(self) -> list[Tuple[str, str]]:
        parts = []
        last_end = 0
        for match in re.finditer(self._VARIABLE_NAME_REGEX, self.text):
            start, end = match.span()
            var_name = match.group(1)
            if start > last_end:
                parts.append(("text", self.text[last_end:start]))
            parts.append(("var", var_name))
            last_end = end
        if last_end < len(self.text):
            parts.append(("text", self.text[last_end:]))
        return parts

    def _merge_adjacent_text_parts(
        self, parts: list[genai_types.Part]
    ) -> list[genai_types.Part]:
        if not parts:
            return []

        merged = []
        current_text_buffer = []

        for part in parts:
            is_purely_text = part.text is not None and all(
                getattr(part, field) is None
                for field in part.model_fields
                if field != "text"
            )

            if is_purely_text:
                current_text_buffer.append(part.text)
            else:
                if current_text_buffer:
                    merged.append(genai_types.Part(text="".join(current_text_buffer)))
                    current_text_buffer = []
                merged.append(part)

        if current_text_buffer:
            merged.append(genai_types.Part(text="".join(current_text_buffer)))

        return merged

    def _is_multimodal_json_string(
        self,
        value: Any,
    ) -> bool:
        """Checks if the input value is a multimodal JSON string."""
        if not isinstance(value, str):
            return False
        try:
            data = json.loads(value)
            # Check for the specific structure: {"contents": [{"parts": [...]}]}
            # or {"parts": [...]} if assemble returns a single Content JSON
            if isinstance(data, dict):
                if "contents" in data and isinstance(data["contents"], list):
                    if not data["contents"]:
                        return False
                    first_content = data["contents"][0]
                    if isinstance(first_content, dict) and "parts" in first_content:
                        try:
                            genai_types.Content.model_validate(first_content)
                            return True
                        except ValueError:
                            return False
                # Adding a check if 'data' itself is a Content-like object with parts
                elif "parts" in data and isinstance(data["parts"], list):
                    try:
                        genai_types.Content.model_validate(data)
                        return True
                    except ValueError:
                        return False
            return False
        except json.JSONDecodeError:
            return False

    def _parse_multimodal_json_string_into_parts(
        self,
        value: str,
    ) -> list[genai_types.Part]:
        """Parses a multimodal JSON string and returns its list of Parts."""
        try:
            content = genai_types.Content.model_validate_json(value)
            return content.parts
        except Exception:
            return [genai_types.Part(text=value)]

    def assemble(self, **kwargs: Any) -> str:
        """Assembles the prompt template with the given keyword arguments.

        Supports both text and multimodal content. The `assemble` method
        substitutes variables from the prompt template text with provided
        values.

        Key Behaviors of `assemble()`:
        1.  Variable Substitution: Replaces all defined variables with their
            corresponding keyword argument values. Raises ValueError if a
            template
            variable is missing a value or if an extraneous kwarg is provided.
        2.  Multimodal Handling:
            - Detects if any variable's value is a JSON string representing
            multimodal content (specifically, `{"contents": [{"parts": [...]}]}`
            or `{"role": "user", "parts": [...]}`).
            - If multimodal content is detected for a variable, its `Part`
            objects
            are extracted and inserted into the assembled sequence.
            - Text segments from the template and simple text variable values
            become `Part(text=...)`.
        3.  Output Format:
            - If ALL substituted variables were simple text AND the assembled
            result (after merging adjacent text parts) consists of a single,
            purely textual `Part`, `assemble()` returns a raw Python string.
            - Otherwise (if any variable was multimodal, or if the assembly
            results in multiple parts or non-textual parts), `assemble()`
            returns
            a JSON string representing a single `google.genai.types.Content`
            object with `role="user"` and the assembled parts.
        4.  Text Part Merging: Consecutively assembled text parts are
            automatically merged into a single text `Part` to create a more
            concise list of parts.

        This dual output format (raw string or JSON string of `Content`) allows
        the downstream inference functions to seamlessly handle both simple text
        prompts and more complex multimodal prompts generated from the same
        templating mechanism.
        """
        current_variables = self.variables
        for var_name_in_kwarg in kwargs:
            if var_name_in_kwarg not in current_variables:
                raise ValueError(
                    f"Invalid variable name '{var_name_in_kwarg}' provided to"
                    " assemble. Valid variables in template are:"
                    f" {current_variables}"
                )
        # Check if all template variables are provided in kwargs
        for tpl_var in current_variables:
            if tpl_var not in kwargs:
                raise ValueError(f"Missing value for template variable '{tpl_var}'.")

        template_segments = self._split_template_by_variables()

        raw_assembled_parts: list[genai_types.Part] = []
        contains_multimodal_variable_type = False

        for segment_type, segment_value in template_segments:
            if segment_type == "text":
                if segment_value:
                    raw_assembled_parts.append(genai_types.Part(text=segment_value))
            elif segment_type == "var":
                var_value = kwargs.get(segment_value)

                str_var_value = str(var_value)

                if self._is_multimodal_json_string(str_var_value):
                    multimodal_parts = self._parse_multimodal_json_string_into_parts(
                        str_var_value
                    )
                    if multimodal_parts:
                        contains_multimodal_variable_type = True
                        raw_assembled_parts.extend(multimodal_parts)
                    else:
                        raw_assembled_parts.append(genai_types.Part(text=str_var_value))
                else:
                    raw_assembled_parts.append(genai_types.Part(text=str_var_value))

        final_assembled_parts = self._merge_adjacent_text_parts(raw_assembled_parts)

        # Condition for returning raw text string:
        # 1. No multimodal variable was *originally* a multimodal JSON string.
        # 2. After merging, there's exactly one part.
        # 3. That single part is purely textual.
        if (
            not contains_multimodal_variable_type
            and len(final_assembled_parts) == 1
            and final_assembled_parts[0].text is not None
            and all(
                getattr(final_assembled_parts[0], field) is None
                for field in final_assembled_parts[0].model_fields
                if field not in ["text", "role"]
            )
        ):
            return final_assembled_parts[0].text

        # Otherwise, construct a Content object (as JSON string).
        final_content_obj = genai_types.Content(parts=final_assembled_parts)
        return final_content_obj.model_dump_json(exclude_none=True)

    def __str__(self) -> str:
        return self.text

    def __repr__(self) -> str:
        return f"PromptTemplate(text='{self.text}')"


class MetricPromptBuilder(PromptTemplate):
    """Builder class for structured LLM-based metric prompt template."""

    criteria: Optional[dict[str, str]] = Field(
        None,
        description="""A dictionary of criteria used to evaluate the model responses.
      The keys are criterion names, and the values are the corresponding
      criterion definitions.
      """,
    )

    rating_scores: Optional[dict[str, str]] = Field(
        None,
        description="""A dictionary mapping of rating score names to their definitions.""",
    )

    @staticmethod
    def _get_default_instruction() -> str:
        """Returns the default instruction for evaluation."""
        return (
            "You are an expert evaluator. Your task is to evaluate the quality"
            " of the responses generated by AI models. We will provide you with"
            " the user prompt and an AI-generated responses.\nYou should first"
            " read the user input carefully for analyzing the task, and then"
            " evaluate the quality of the responses based on the Criteria"
            " provided in the Evaluation section below.\nYou will assign the"
            " response a rating following the Rating Scores and Evaluation"
            " Steps. Give step by step explanations for your rating, and only"
            " choose ratings from the Rating Scores."
        )

    instruction: Optional[str] = Field(
        default_factory=lambda: MetricPromptBuilder._get_default_instruction(),
        description="""The general instruction to guide the model in performing the evaluation.
    If not provided, a default instruction for evaluation will be used.
    """,
    )

    metric_definition: Optional[str] = Field(
        None,
        description="""An optional high-level description of the metric to be evaluated.
      If not provided, this field will not be included in the prompt template.
      """,
    )

    @staticmethod
    def _get_default_evaluation_steps() -> dict[str, str]:
        """Returns the default evaluation steps for metric evaluation."""
        return {
            "Step 1": (
                "Assess the response in aspects of all criteria provided."
                " Provide assessment according to each criterion."
            ),
            "Step 2": (
                "Score based on the Rating Scores. Give a brief rationale to"
                " explain your evaluation considering each individual"
                " criterion."
            ),
        }

    evaluation_steps: Optional[dict[str, str]] = Field(
        default_factory=lambda: MetricPromptBuilder._get_default_evaluation_steps(),
        description="""An optional dictionary of evaluation steps.
      The keys are the names of the evaluation steps, and the values are
      descriptions of the corresponding evaluation steps. If not provided,
      default metric evaluation steps will be used.
      """,
    )

    few_shot_examples: Optional[list[str]] = Field(
        None,
        description="""An optional list of few-shot examples to guide the model's evaluation.
      These examples demonstrate how to apply the criteria, rating scores,
      and evaluation steps to assess model responses. Providing few-shot examples
      can improve the accuracy of the evaluation. If not provided, this field
      will not be included in the prompt template.
      """,
    )

    @staticmethod
    def _serialize_dict_in_order(elements: Optional[dict[str, str]]) -> str:
        """Serializes dictionary to ordered string value without brackets."""
        if elements is None:
            return ""
        return "\n".join(f"{key}: {value}" for key, value in sorted(elements.items()))

    @model_validator(mode="before")
    @classmethod
    def _prepare_fields_and_construct_text(cls, data: Any) -> Any:
        """Pydantic model validator (before mode) to prepare and construct prompt text.

        This validator performs the following actions:
          1. Apply default logic for fields (instruction, evaluation_steps).
          2. Construct the 'text' string from all components.
          3. Ensure 'text' is in the data dictionary for PromptTemplate
          initialization.

        Args:
            data: Input data for the model, either a dictionary or an existing
              model instance.

        Returns:
            Processed data dictionary with the 'text' field constructed.
        """
        if not isinstance(data, dict):
            return data

        if "text" in data:
            raise ValueError(
                "The 'text' field is automatically constructed and should not"
                " be provided manually."
            )

        if data.get("criteria") is None or data.get("rating_scores") is None:
            raise ValueError(
                "Both 'criteria' and 'rating_scores' are required to construct"
                " theLLM-based metric prompt template text."
            )

        instruction = data.get("instruction", cls._get_default_instruction())
        metric_definition = data.get("metric_definition")
        evaluation_steps = data.get(
            "evaluation_steps", cls._get_default_evaluation_steps()
        )
        criteria = data.get("criteria")
        rating_scores = data.get("rating_scores")
        few_shot_examples = data.get("few_shot_examples")

        template_parts = [
            "# Instruction",
            instruction,
            "\n",
            "# Evaluation",
        ]

        sections = {
            "Metric Definition": metric_definition,
            "Criteria": cls._serialize_dict_in_order(criteria),
            "Rating Scores": cls._serialize_dict_in_order(rating_scores),
            "Evaluation Steps": cls._serialize_dict_in_order(evaluation_steps),
            "Evaluation Examples": (
                "\n".join(few_shot_examples) if few_shot_examples else None
            ),
        }

        for title, content in sections.items():
            if content:
                template_parts.extend([f"## {title}", f"{content}\n"])

        template_parts.extend(
            [
                "\n# User Inputs and AI-generated Response",
                "## User Inputs",
            ]
        )

        template_parts.extend(["## AI-generated Response", "{response}"])
        constructed_text = "\n".join(template_parts)

        data["text"] = constructed_text
        return data

    def __str__(self) -> str:
        """Returns the fully constructed prompt template text."""
        return self.text


class PromptTemplateDict(TypedDict, total=False):
    """A prompt template for creating prompts with variables."""

    text: Optional[str]
    """The prompt template text."""


PromptTemplateOrDict = Union[PromptTemplate, PromptTemplateDict]


class EvalRunInferenceConfig(_common.BaseModel):
    """Optional parameters for inference."""

    dest: Optional[str] = Field(
        default=None,
        description="""The destination path for the inference results.""",
    )
    prompt_template: Optional[Union[str, PromptTemplate]] = Field(
        default=None,
        description="""The prompt template to use for inference.""",
    )
    generate_content_config: Optional[genai_types.GenerateContentConfig] = Field(
        default=None,
        description="""The config for the generate content call.""",
    )


class EvalRunInferenceConfigDict(TypedDict, total=False):
    """Optional parameters for inference."""

    dest: Optional[str]
    """The destination path for the inference results."""

    prompt_template: Optional[Union[str, PromptTemplateDict]]
    """The prompt template to use for inference."""

    generate_content_config: Optional[genai_types.GenerateContentConfig]
    """The config for the generate content call."""


EvalRunInferenceConfigOrDict = Union[EvalRunInferenceConfig, EvalRunInferenceConfigDict]


class EvalCaseMetricResult(_common.BaseModel):
    """Evaluation result for a single evaluation case for a single metric."""

    metric_name: Optional[str] = Field(
        default=None, description="""Name of the metric."""
    )
    score: Optional[float] = Field(default=None, description="""Score of the metric.""")
    explanation: Optional[str] = Field(
        default=None, description="""Explanation of the metric."""
    )
    raw_output: Optional[list[str]] = Field(
        default=None, description="""Raw output of the metric."""
    )
    rubrics: Optional[list[str]] = Field(
        default=None,
        description="""A list of rubrics used to evaluate the example for rubric-based metrics.""",
    )
    error_message: Optional[str] = Field(
        default=None, description="""Error message for the metric."""
    )


class EvalCaseMetricResultDict(TypedDict, total=False):
    """Evaluation result for a single evaluation case for a single metric."""

    metric_name: Optional[str]
    """Name of the metric."""

    score: Optional[float]
    """Score of the metric."""

    explanation: Optional[str]
    """Explanation of the metric."""

    raw_output: Optional[list[str]]
    """Raw output of the metric."""

    rubrics: Optional[list[str]]
    """A list of rubrics used to evaluate the example for rubric-based metrics."""

    error_message: Optional[str]
    """Error message for the metric."""


EvalCaseMetricResultOrDict = Union[EvalCaseMetricResult, EvalCaseMetricResultDict]


class ResponseCandidateResult(_common.BaseModel):
    """Aggregated metric results for a single response candidate of an EvalCase."""

    response_index: Optional[int] = Field(
        default=None,
        description="""Index of the response candidate this result pertains to.""",
    )
    metric_results: Optional[dict[str, EvalCaseMetricResult]] = Field(
        default=None,
        description="""A dictionary of metric results for this response candidate, keyed by metric name.""",
    )


class ResponseCandidateResultDict(TypedDict, total=False):
    """Aggregated metric results for a single response candidate of an EvalCase."""

    response_index: Optional[int]
    """Index of the response candidate this result pertains to."""

    metric_results: Optional[dict[str, EvalCaseMetricResultDict]]
    """A dictionary of metric results for this response candidate, keyed by metric name."""


ResponseCandidateResultOrDict = Union[
    ResponseCandidateResult, ResponseCandidateResultDict
]


class EvalCaseResult(_common.BaseModel):
    """Eval result for a single evaluation case."""

    eval_case_index: Optional[int] = Field(
        default=None, description="""Index of the evaluation case."""
    )
    response_candidate_results: Optional[list[ResponseCandidateResult]] = Field(
        default=None,
        description="""A list of results, one for each response candidate of the EvalCase.""",
    )


class EvalCaseResultDict(TypedDict, total=False):
    """Eval result for a single evaluation case."""

    eval_case_index: Optional[int]
    """Index of the evaluation case."""

    response_candidate_results: Optional[list[ResponseCandidateResultDict]]
    """A list of results, one for each response candidate of the EvalCase."""


EvalCaseResultOrDict = Union[EvalCaseResult, EvalCaseResultDict]


class AggregatedMetricResult(_common.BaseModel):
    """Evaluation result for a single metric for an evaluation dataset."""

    metric_name: Optional[str] = Field(
        default=None, description="""Name of the metric."""
    )
    num_cases_total: Optional[int] = Field(
        default=None, description="""Total number of cases in the dataset."""
    )
    num_cases_valid: Optional[int] = Field(
        default=None, description="""Number of valid cases in the dataset."""
    )
    num_cases_error: Optional[int] = Field(
        default=None,
        description="""Number of cases with errors in the dataset.""",
    )
    mean_score: Optional[float] = Field(
        default=None, description="""Mean score of the metric."""
    )
    stdev_score: Optional[float] = Field(
        default=None, description="""Standard deviation of the metric."""
    )
    win_rate: Optional[dict[str, float]] = Field(
        default=None,
        description="""A dictionary of win rates for each response.""",
    )

    # Allow extra fields to support custom aggregation stats.
    model_config = ConfigDict(extra="allow")


class AggregatedMetricResultDict(TypedDict, total=False):
    """Evaluation result for a single metric for an evaluation dataset."""

    metric_name: Optional[str]
    """Name of the metric."""

    num_cases_total: Optional[int]
    """Total number of cases in the dataset."""

    num_cases_valid: Optional[int]
    """Number of valid cases in the dataset."""

    num_cases_error: Optional[int]
    """Number of cases with errors in the dataset."""

    mean_score: Optional[float]
    """Mean score of the metric."""

    stdev_score: Optional[float]
    """Standard deviation of the metric."""

    win_rate: Optional[dict[str, float]]
    """A dictionary of win rates for each response."""


AggregatedMetricResultOrDict = Union[AggregatedMetricResult, AggregatedMetricResultDict]


class EvaluationRunMetadata(_common.BaseModel):
    """Metadata for an evaluation run."""

    candidate_names: Optional[list[str]] = Field(
        default=None,
        description="""Name of the candidate(s) being evaluated in the evaluation run.""",
    )
    dataset_name: Optional[str] = Field(
        default=None,
        description="""Name of the evaluation dataset used for the evaluation run.""",
    )
    dataset_id: Optional[str] = Field(
        default=None,
        description="""Unique identifier for the evaluation dataset used for the evaluation run.""",
    )
    creation_timestamp: Optional[datetime.datetime] = Field(
        default=None,
        description="""Creation timestamp of the evaluation run.""",
    )


class EvaluationRunMetadataDict(TypedDict, total=False):
    """Metadata for an evaluation run."""

    candidate_names: Optional[list[str]]
    """Name of the candidate(s) being evaluated in the evaluation run."""

    dataset_name: Optional[str]
    """Name of the evaluation dataset used for the evaluation run."""

    dataset_id: Optional[str]
    """Unique identifier for the evaluation dataset used for the evaluation run."""

    creation_timestamp: Optional[datetime.datetime]
    """Creation timestamp of the evaluation run."""


EvaluationRunMetadataOrDict = Union[EvaluationRunMetadata, EvaluationRunMetadataDict]


class EvaluationResult(_common.BaseModel):
    """Result of an evaluation run for an evaluation dataset."""

    eval_case_results: Optional[list[EvalCaseResult]] = Field(
        default=None,
        description="""A list of evaluation results for each evaluation case.""",
    )
    summary_metrics: Optional[list[AggregatedMetricResult]] = Field(
        default=None,
        description="""A list of summary-level evaluation results for each metric.""",
    )
    metadata: Optional[EvaluationRunMetadata] = Field(
        default=None, description="""Metadata for the evaluation run."""
    )


class EvaluationResultDict(TypedDict, total=False):
    """Result of an evaluation run for an evaluation dataset."""

    eval_case_results: Optional[list[EvalCaseResultDict]]
    """A list of evaluation results for each evaluation case."""

    summary_metrics: Optional[list[AggregatedMetricResultDict]]
    """A list of summary-level evaluation results for each metric."""

    metadata: Optional[EvaluationRunMetadataDict]
    """Metadata for the evaluation run."""


EvaluationResultOrDict = Union[EvaluationResult, EvaluationResultDict]


class EvaluateMethodConfig(_common.BaseModel):
    """Optional parameters for the evaluate method."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )
    dataset_schema: Optional[Literal["gemini", "flatten"]] = Field(
        default=None,
        description="""The schema to use for the dataset.
      If not specified, the dataset schema will be inferred from the first
      example in the dataset.""",
    )
    dest: Optional[str] = Field(
        default=None,
        description="""The destination path for the evaluation results.""",
    )


class EvaluateMethodConfigDict(TypedDict, total=False):
    """Optional parameters for the evaluate method."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""

    dataset_schema: Optional[Literal["gemini", "flatten"]]
    """The schema to use for the dataset.
      If not specified, the dataset schema will be inferred from the first
      example in the dataset."""

    dest: Optional[str]
    """The destination path for the evaluation results."""


EvaluateMethodConfigOrDict = Union[EvaluateMethodConfig, EvaluateMethodConfigDict]
